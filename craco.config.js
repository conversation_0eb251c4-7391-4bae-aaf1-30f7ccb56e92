const path = require("path");
const TerserPlugin = require("terser-webpack-plugin");
const CompressionWebpackPlugin = require("compression-webpack-plugin");

module.exports = {
  webpack: {
    alias: {
      "@": path.resolve(__dirname, "src"), // 确保路径正确
    },
    plugins: [
      new CompressionWebpackPlugin({
        filename: "[path][base].gz",
        algorithm: "gzip",
        test: /\.(js|css|html|svg)$/,
        threshold: 10240,
        minRatio: 0.8,
      }),
    ],
    configure: (webpackConfig) => {
      if (webpackConfig.mode === "production") {
        // 移除 Source Map
        webpackConfig.devtool = false;

        // 配置 TerserPlugin
        webpackConfig.optimization.minimizer = [
          new TerserPlugin({
            terserOptions: {
              compress: {
                drop_console: false, // 移除 console.log
                drop_debugger: true, // 移除 debugger
              },
              mangle: true, // 变量名混淆
              module:true // 优化es6
            },
            parallel:true, // 并行压缩
          }),
        ];
      }
      return webpackConfig;
    },
  },
  style: {
    modules: {
      localIdentName: "[name]__[local]___[hash:base64:5]",
    },
    postcss: {
      mode: "extends",
      loaderOptions: {
        postcssOptions: {
          plugins: [
            [
              "postcss-pxtorem",
              {
                rootValue: 39.2, // 设计稿宽度/10（如 375px 设计稿设为 37.5）
                propList: ["*"], // 转换所有 CSS 属性的 px 单位
                unitPrecision: 5, // 保留小数点位数
                minPixelValue: 1, // 最小转换像素值
                exclude: /node_modules/i,
                selectorBlackList: ["ignore-"],
                mediaQuery: false,
                replace: true
              },
            ],
          ],
        },
      },
    },
  },
  // 添加开发服务器代理配置
  devServer: {
    proxy: {
      '/baidupan':{
        target: 'http://192.168.0.221/plugin/**********',
        changeOrigin: true,
      },
      '/timemachine': {
        target: 'http://192.168.33.221/plugin/**********',
        changeOrigin: true
      }
    }
  }
};
