import { PreloadImage } from '@/components/Image';
import styles from './index.module.scss';
import moreIcon from '@/Resources/filmWall/more.png';
import { Button, Tag, Popover, Toast } from 'antd-mobile';
import { PlayOutline, HeartOutline, HeartFill, CheckOutline, DownlandOutline } from 'antd-mobile-icons';

import React, { useEffect, useState, useCallback, useMemo } from 'react';
import NavigatorBar from '@/components/NavBar';
import { useHistory, useLocation } from 'react-router-dom';
import SearchSelector, { SearchStatus } from './SearchSelector';
import { modalShow } from '@/components/List';
import { px2rem } from '@/utils/setRootFontSize';
import { searchOnlineMedia, mediaProps, fileRematch, fileDelete, move2trashbin, getFilePath, collectEpisode, markWatchedEpisode, MediaFileInfo } from '@/api/fatWall';
import { useRequest } from 'ahooks';
import { playVideo, downloadFiles } from '@/api/fatWallJSBridge';
import CommonUtils from '@/utils/CommonUtils';

// 格式化秒为时分秒的函数
const formatDuration = (seconds: number | string): string => {
    const numSeconds = typeof seconds === 'string' ? parseInt(seconds, 10) : seconds;
    
    if (isNaN(numSeconds) || numSeconds < 0) {
        return '未知时长';
    }
    
    const hours = Math.floor(numSeconds / 3600);
    const minutes = Math.floor((numSeconds % 3600) / 60);
    const remainingSeconds = numSeconds % 60;
    
    if (hours > 0) {
        return `${hours}小时${minutes}分钟${remainingSeconds}秒`;
    } else if (minutes > 0) {
        return `${minutes}分钟${remainingSeconds}秒`;
    } else {
        return `${remainingSeconds}秒`;
    }
};

// 类型定义
interface Actor {
    name: string;
    role: string;
}

interface FileInfo {
    path: string;
    size: string;
}

// 版本类型定义
interface Version {
    id: string;
    name: string;
}

interface VideoInfo {
    title: string;
    score: string;
    year: string;
    category: string;
    duration: string;
    source: string;
    posterUrl: string;
    actors: Actor[];
    fileInfo: FileInfo;
    synopsis: string;
    versions: Version[];
    name: string;
    kind: string[];
}

interface VideoDetailsProps {
    path: string;
}

const VideoDetails: React.FC<VideoDetailsProps> = (props) => {
    const history = useHistory();
    const location = useLocation();
    console.log(location.state);
    
    // 获取从FilmCard传递过来的数据
    const {filmInfo, allMediaFiles } = location.state as {
        isDrama: boolean;
        allMediaFiles: MediaFileInfo[]; // 完整的剧集文件列表
        filmInfo: {
            subtitle_index: any;
            subtitle_type: any;
            subtitle_path: any;
            audio_index: any;
            title: string;
            setNum: number;
            poster: string;
            progress: number;
            time: string;
            type: string;
            score: string;
            year: string;
            kind: string[];
            category: string;
            classes: string;
            duration: string;
            source: string;
            posterUrl: string;
            actors: { name: string; role: string }[];
            fileInfo: { path: string; size: string };
            synopsis: string;
            versions: { id: string; name: string }[];
            file_id: number;
            episode: number;
            session: number;
            resolution: string;
            hdr: string;
            audio_codec: string;
            last_play_point: number;
            name: string;
            media_id?: number;
            lib_id?: number;
            file_media_id?: number;
            favourite?: number; // 收藏状态：0未收藏，1已收藏
            seen?: number; // 观看状态：0未观看，1已观看
        }
    };

    const [headerStyle, setHeaderStyle] = useState<React.CSSProperties>({});
    const [isFavorite, setIsFavorite] = useState<boolean>(false);
    const [isCheck, setIsCheck] = useState<boolean>(false);
    // const [selectedVersionId, ] = useState<string>('1');
    const [showMatchCorrection, setShowMatchCorrection] = useState<boolean>(false);
    const [searchKeyword, setSearchKeyword] = useState<string>('');
    const [searchStatus, setSearchStatus] = useState<SearchStatus>('idle');
    const [searchResults, setSearchResults] = useState<mediaProps[]>([]);
    const [selectedMatch, setSelectedMatch] = useState<mediaProps | null>(null);
    const [morePopoverVisible, setMorePopoverVisible] = useState<boolean>(false);
    const [currentFavoriteOperation, setCurrentFavoriteOperation] = useState<'collect' | 'uncollect' | null>(null);
    const [currentWatchedOperation, setCurrentWatchedOperation] = useState<'watched' | 'unwatched' | null>(null);
    // 使用传递过来的完整剧集列表，而不是通过API获取
    // eslint-disable-next-line react-hooks/exhaustive-deps
    const mediaFiles = allMediaFiles || [];

    const videoInfo: VideoInfo = useMemo(() => {
        if (!filmInfo) {
            // 如果没有传递数据，返回默认值
            return {
                title: '加载中...',
                score: '0',
                year: '0',
                kind: ['加载中'],
                category: '加载中',
                duration: '0分钟',
                source: '媒体库名称',
                posterUrl: '',
                actors: [],
                fileInfo: {
                    path: '',
                    size: ''
                },
                synopsis: '加载中...',
                versions: [],
                name: ''
            };
        }

        // 使用传递过来的真实数据
        return {
            title: filmInfo.title,
            score: filmInfo.score,
            year: filmInfo.year,
            kind: filmInfo.kind,
            category: filmInfo.category,
            duration: typeof filmInfo.duration === 'number' ? formatDuration(filmInfo.duration) : filmInfo.duration,
            source: filmInfo.source,
            posterUrl: filmInfo.posterUrl || filmInfo.poster,
            actors: filmInfo.actors || [],
            fileInfo: filmInfo.fileInfo,
            synopsis: filmInfo.synopsis,
            versions: filmInfo.versions || [
                { id: '1', name: `${filmInfo.resolution} | ${filmInfo.hdr} | ${filmInfo.audio_codec}` }
            ],
            name: filmInfo.name
        };
    }, [filmInfo]);

    // 移除API获取，直接使用传递过来的完整剧集列表
    console.log('SingleEpisode接收到完整剧集列表:', mediaFiles);

    // 搜索请求
    const { run: runSearchOnlineMedia } = useRequest(
        async (keyword: string) => {
            if (!keyword.trim()) return [];
            const res = await searchOnlineMedia(keyword);
            let data: mediaProps[] = [];
            if (Array.isArray(res.data)) {
                data = res.data;
            } else if (res.data && Array.isArray((res.data as any).medias)) {
                data = (res.data as any).medias;
            }
            return data;
        },
        {
            manual: true,
            onSuccess: (data) => {
                if (!data || !data.length) {
                    setSearchStatus('empty');
                    setSearchResults([]);
                } else {
                    setSearchStatus('success');
                    setSearchResults(data);
                }
            },
            onError: () => {
                setSearchStatus('error');
                setSearchResults([]);
            },
        }
    );

    const { run: runFileRematch } = useRequest(
        fileRematch,
        {
            manual: true,
            onSuccess: () => {
                Toast.show({
                    content: '修正成功',
                    position: 'bottom',
                    duration: 1500,
                });
            },
            onError: () => {
                Toast.show({
                    content: '修正失败，请重试',
                    position: 'bottom',
                    duration: 1500,
                });
            },
        }
    );

    // 单集收藏/取消收藏
    const { run: runCollectEpisode } = useRequest(
        collectEpisode,
        {
            manual: true,
            onSuccess: (res) => {
                if (res.code === 0) {
                    // 根据操作类型显示不同的消息
                    const successMessage = currentFavoriteOperation === 'collect' ? '已添加到收藏' : '已取消收藏';
                    Toast.show({
                        content: successMessage,
                        position: 'bottom',
                        duration: 1500,
                    });
                    // 成功时不需要重新获取数据，因为本地状态已经更新了
                } else {
                    Toast.show({
                        content: '操作失败，请重试',
                        position: 'bottom',
                        duration: 1500,
                    });
                    // 失败时恢复状态
                    setIsFavorite(prev => !prev);
                }
                // 清空操作类型
                setCurrentFavoriteOperation(null);
            },
            onError: () => {
                Toast.show({
                    content: '操作失败，请重试',
                    position: 'bottom',
                    duration: 1500,
                });
                // 失败时恢复状态
                setIsFavorite(prev => !prev);
                // 清空操作类型
                setCurrentFavoriteOperation(null);
            },
        }
    );

    // 单集标记已观看/未观看
    const { run: runMarkWatchedEpisode } = useRequest(
        markWatchedEpisode,
        {
            manual: true,
            onSuccess: (res) => {
                if (res.code === 0) {
                    // 根据操作类型显示不同的消息
                    const successMessage = currentWatchedOperation === 'watched' ? '已标记为观看' : '已取消观看';
                    Toast.show({
                        content: successMessage,
                        position: 'bottom',
                        duration: 1500,
                    });
                    // 成功时不需要重新获取数据，因为本地状态已经更新了
                } else {
                    Toast.show({
                        content: '操作失败，请重试',
                        position: 'bottom',
                        duration: 1500,
                    });
                    // 失败时恢复状态
                    setIsCheck(prev => !prev);
                }
                // 清空操作类型
                setCurrentWatchedOperation(null);
            },
            onError: () => {
                Toast.show({
                    content: '操作失败，请重试',
                    position: 'bottom',
                    duration: 1500,
                });
                // 失败时恢复状态
                setIsCheck(prev => !prev);
                // 清空操作类型
                setCurrentWatchedOperation(null);
            },
        }
    );

    // 移除不必要的文件状态获取API调用
    // SingleEpisode组件直接使用传入的filmInfo中的状态即可

    const { run: runFileDelete } = useRequest(
        fileDelete,
        {
            manual: true,
            onSuccess: () => {
                Toast.show({
                    content: '删除成功',
                    position: 'bottom',
                    duration: 1500,
                });
            },
            onError: () => {
                Toast.show({
                    content: '删除失败，请重试',
                    position: 'bottom',
                    duration: 1500,
                });
            },
        }
    );
        const { run: runGetFilePath } = useRequest(
            getFilePath,
            {
                manual: true,
                onSuccess: (res) => {
                    if (res.code === 0 && res.data && res.data.path) {
                        // 获取到文件路径后，调用移动到回收站接口
                        runMove2trashbin({ path: res.data.path });
                        // 显示删除进度弹窗
                        // showDeleteProgressModal();
                        Toast.show({
                            content: '删除成功',
                            position: 'bottom',
                            duration: 1500,
                        });
                    } else {
                        Toast.show({
                            content: '获取文件路径失败',
                            position: 'bottom',
                            duration: 1500,
                        });
                    }
                },
                onError: () => {
                    Toast.show({
                        content: '获取文件路径失败',
                        position: 'bottom',
                        duration: 1500,
                    });
                }
            }
        );
    

    const { run: runMove2trashbin } = useRequest(
        move2trashbin,
        {
            manual: true,
            onSuccess: () => {
                Toast.show({
                    content: '删除成功',
                    position: 'bottom',
                    duration: 1500,
                });
            },
            onError: () => {
                Toast.show({
                    content: '删除失败，请重试',
                    position: 'bottom',
                    duration: 1500,
                });
            },
        }
    );

    // 处理搜索
    const handleSearch = useCallback((keyword: string) => {
        setSearchKeyword(keyword);
        // setSearchResults(mockSearchResults) //mock数据后续删除
        if (!keyword.trim()) return;
        setSearchStatus('loading');
        runSearchOnlineMedia(keyword);
    }, [runSearchOnlineMedia]);

    // 处理选择搜索结果
    const handleSelectMatch = useCallback((item: mediaProps) => {
        setSelectedMatch(item);
    }, []);

    // 处理确认修正
    const handleConfirmMatch = useCallback((selectedItem: mediaProps | null) => {
        if (selectedItem) {
            // 查找索引
            const index = (selectedItem as any).index !== undefined ? (selectedItem as any).index : 0;
            if (filmInfo && filmInfo.file_id) {
                runFileRematch({
                    file_id: filmInfo.file_id,
                    keyword: searchKeyword,
                    index,
                });
            }
            setShowMatchCorrection(false);
            // 清空状态
            setSearchKeyword('');
            setSearchResults([]);
            setSelectedMatch(null);
            setSearchStatus('idle');
        } else {
            Toast.show({
                content: '请先选择一个匹配结果',
                position: 'bottom',
                duration: 1500,
            });
        }
    }, [searchKeyword, runFileRematch, filmInfo]);

    // 重置搜索状态
    const handleResetSearchStatus = useCallback(() => {
        setSearchStatus('idle');
        setSearchResults([]);
        setSelectedMatch(null);
    }, []);

    // // 显示删除进度弹窗
    // const showDeleteProgressModal = () => {
    //     let progress = 20; // 初始进度为20%
    //     let timer: NodeJS.Timeout; // 定义定时器变量

    //     modalShow(
    //         '',
    //         <div>
    //             <div className={styles.progressModal}>删除中...{progress}%</div>
    //             <ProgressBar
    //                 percent={progress}
    //                 style={{
    //                     '--track-width': '8px',
    //                     '--fill-color': 'var(--primary-color)',
    //                 }}
    //             />
    //         </div>,
    //         () => null,
    //         () => {
    //             // 点击后台运行按钮时清除定时器并关闭弹窗
    //             clearInterval(timer);
    //             return null;
    //         },
    //         false,
    //         {
    //             okBtnStyle: { display: 'none' },
    //             cancelBtnStyle: { width: px2rem('300px'), margin: 0 },
    //             position: 'bottom',
    //             cancelBtnText: '后台运行'
    //         }
    //     );
    //     // 模拟进度更新，但不会自动关闭弹窗
    //     timer = setInterval(() => {
    //         progress += 5;

    //         // 当进度达到100%时停止更新，但不关闭弹窗
    //         if (progress >= 100) {
    //             clearInterval(timer);
    //         }
    //     }, 300);
    // };

    const move2Trashbin = useCallback((m) => {
        m.destroy();
        if (filmInfo && filmInfo.file_id) {
            runFileDelete({ file_id: filmInfo.file_id });
        }
        Toast.show({
            content: '已从媒体库移除',
            position: 'bottom',
            duration: 1500,
        });
    }, [runFileDelete, filmInfo]);

    const delFile = useCallback((modal) => {
        modalShow(`是否确定删除文件？`, <>删除的文件将移至"回收站"，保留30天</>, (m => {
            m.destroy();
            modal.destroy();

            // 先获取文件路径，然后再移动到回收站
            runGetFilePath({ media_ids: [filmInfo.file_media_id || 0], lib_id: filmInfo.lib_id || 0 });
            // 显示删除进度弹窗
            // showDeleteProgressModal();
        }), () => null, false, { position: 'bottom', okBtnText: '确定', okBtnStyle: { backgroundColor: 'var(--cancel-btn-background-color)', color: 'red' }, bottom: '80px'  })
    }, [runGetFilePath, filmInfo])

    // 处理删除
    const handleDelete = useCallback(() => {
        setMorePopoverVisible(false);
        const m = modalShow('确认删除吗？', (
            <>
                <div className={styles.modalButton} onClick={() => move2Trashbin(m)}>仅从媒体库移除</div>
                <div className={styles.modalButton} style={{ color: 'var(--emergency-text-color)' }} onClick={() => delFile(m)}>删除文件</div>
            </>
        ), () => null, () => null, false, { okBtnStyle: { display: 'none' }, cancelBtnStyle: { width: px2rem('300px'), margin: 0 }, position: 'bottom', bottom: '80px'  })
    }, [delFile, move2Trashbin]);

    // 处理修改匹配信息
    const handleEditMatch = useCallback(() => {
        setMorePopoverVisible(false);
        setShowMatchCorrection(true);
        setSearchStatus('idle');
        setSearchResults([]);
        setSelectedMatch(null);
    }, []);

    const rightSize = useMemo(() => {
        return (
            <Popover
                className={styles.morePopoverContainer}
                visible={morePopoverVisible}
                onVisibleChange={setMorePopoverVisible}
                content={
                    <div className={styles.morePopover}>
                        <div className={styles.morePopoverItem} onClick={handleEditMatch}>
                            <span className={styles.morePopoverText}>修正匹配信息</span>
                        </div>
                        <div className={styles.morePopoverItem} style={{ color: '#FF3B3B' }} onClick={handleDelete}>
                            <span className={styles.morePopoverText}>删除</span>
                        </div>
                    </div>
                }
                trigger='click'
                placement='bottom-end'
                style={{ '--arrow-size': '0px' } as React.CSSProperties}
            >
                <div className={styles.right} onClick={() => setMorePopoverVisible(true)}>
                    <PreloadImage src={moreIcon} alt="more" />
                </div>
            </Popover>
        )
    }, [morePopoverVisible, handleEditMatch, handleDelete]);


    const toggleFavorite = useCallback(() => {
        if (!filmInfo?.file_id) {
            Toast.show({
                content: '无法获取剧集文件ID',
                position: 'bottom',
                duration: 1500,
            });
            return;
        }

        const newFavoriteStatus = !isFavorite;
        const operationType = newFavoriteStatus ? 'collect' : 'uncollect';
        
        // 保存当前操作类型
        setCurrentFavoriteOperation(operationType);
        
        // 先更新本地状态，提供即时反馈
        setIsFavorite(newFavoriteStatus);

        // 调用API
        runCollectEpisode({
            file_id: filmInfo.file_id,
            favourite: newFavoriteStatus ? 1 : 0
        });
    }, [isFavorite, filmInfo, runCollectEpisode]);

    const toggleCheck = useCallback(() => {
        if (!filmInfo?.file_id) {
            Toast.show({
                content: '无法获取剧集文件ID',
                position: 'bottom',
                duration: 1500,
            });
            return;
        }

        const newWatchedStatus = !isCheck;
        const operationType = newWatchedStatus ? 'watched' : 'unwatched';
        
        // 保存当前操作类型
        setCurrentWatchedOperation(operationType);
        
        // 先更新本地状态，提供即时反馈
        setIsCheck(newWatchedStatus);

        // 调用API
        runMarkWatchedEpisode({
            file_id: filmInfo.file_id,
            seen: newWatchedStatus ? 1 : 0
        });
    }, [isCheck, filmInfo, runMarkWatchedEpisode]);

    // 处理播放按钮点击
    const handlePlay = useCallback(() => {
        if (!filmInfo || !filmInfo.fileInfo?.path) {
            Toast.show({
                content: '暂无可播放的文件',
                position: 'bottom',
                duration: 1500,
            });
            return;
        }

        // 如果有完整的剧集列表，传递完整列表；否则只传递当前单集
        let videoList;
        let playIndex = 0;

        if (mediaFiles && mediaFiles.length > 0) {
            // 构建完整的播放列表
            videoList = mediaFiles.map(file => ({
                path: file.path,
                media_id: (file.media_id || filmInfo.media_id || 0).toString(),
                file_id: file.file_id.toString(),
                duration: file.duration || 0, // 视频时长
                position: file.last_play_point || 0, // 断点信息
                isCompelete: file.seen, // 是否完整播放
                audioIndex: file.audio_index, // 音轨信息
                subtitlePath: file.subtitle_path, // 字幕路径
                subtitleType: file.subtitle_type, // 字幕类型，0表示内嵌字幕
                subtitleIndex: file.subtitle_index, // 字幕索引
            }));

            // 找到当前单集在完整列表中的索引位置
            const currentEpisodeIndex = mediaFiles.findIndex(file => file.file_id === filmInfo.file_id);
            playIndex = currentEpisodeIndex !== -1 ? currentEpisodeIndex : 0;
            
            console.log(`SingleEpisode播放：当前第${filmInfo.episode}集，在完整列表中的索引位置：${playIndex}，总共${mediaFiles.length}集`);
        } else {
            // 如果没有获取到完整列表，则只传递当前单集（兼容模式）
            videoList = [{
                path: filmInfo.fileInfo.path,
                media_id: filmInfo.media_id?.toString() || '0',
                file_id: filmInfo.file_id?.toString() || '0',
                duration: typeof filmInfo.duration === 'number' ? filmInfo.duration : 0, // 视频时长，确保为number
                position: filmInfo.last_play_point || 0, // 断点信息
                isCompelete: filmInfo.seen, // 是否完整播放
                audioIndex: filmInfo.audio_index, // 音轨信息
                subtitlePath: filmInfo.subtitle_path, // 字幕路径
                subtitleType: filmInfo.subtitle_type, // 字幕类型，0表示内嵌字幕
                subtitleIndex: filmInfo.subtitle_index, // 字幕索引
            }];
            
            console.log('SingleEpisode播放：使用单集模式（未获取到完整列表）');
        }

        // 调用视频播放接口
        playVideo(videoList, playIndex, (res) => {
            if (res.code === 0) {
                Toast.show({
                    content: '开始播放',
                    position: 'bottom',
                    duration: 1500,
                });
            } else {
                Toast.show({
                    content: `播放失败: ${res.msg}`,
                    position: 'bottom',
                    duration: 1500,
                });
            }
        }).catch((error) => {
            Toast.show({
                content: error.message || '播放失败',
                position: 'bottom',
                duration: 1500,
            });
        });
    }, [filmInfo, mediaFiles]);

    // 处理下载按钮点击
    const handleDownload = useCallback(() => {
        if (!filmInfo || !filmInfo.fileInfo.path) {
            Toast.show({
                content: '暂无可下载的文件',
                position: 'bottom',
                duration: 1500,
            });
            return;
        }

        // 构造文件信息列表
        const fileList = [{
            name: filmInfo.fileInfo.path.split('/').pop() || `第${filmInfo.episode}集`,
            path: filmInfo.fileInfo.path,
            mtime: '', // 设为空字符串
            size: 0 // 这里没有具体的文件大小信息
        }];

        // 调用下载接口
        downloadFiles(fileList, (res) => {
            if (res.code === 0) {
                Toast.show({
                    content: '开始下载',
                    position: 'bottom',
                    duration: 1500,
                });
            } else {
                Toast.show({
                    content: `下载失败: ${res.msg}`,
                    position: 'bottom',
                    duration: 1500,
                });
            }
        }).catch((error) => {
            Toast.show({
                content: error.message || '下载失败',
                position: 'bottom',
                duration: 1500,
            });
        });
    }, [filmInfo]);


    // 获取当前选择的版本名称
    // const selectedVersionName = useMemo(() => {
    //     const selectedVersion = videoInfo.versions.find(version => version.id === selectedVersionId);
    //     return selectedVersion ? selectedVersion.name : videoInfo.versions[0].name;
    // }, [selectedVersionId, videoInfo.versions]);

    useEffect(() => {
        setHeaderStyle({
            backgroundImage: `url(${videoInfo.posterUrl})`
        });
    }, [videoInfo.posterUrl]);

    // 初始化收藏和观看状态
    useEffect(() => {
        if (filmInfo) {
            // 直接使用传递的数据进行初始化，不需要额外的API调用
            setIsFavorite(filmInfo.favourite === 1);
            setIsCheck(filmInfo.seen === 1);
        }
    }, [filmInfo]);

    return (
        <div className={styles.container}>
            <SearchSelector
                visible={showMatchCorrection}
                onClose={() => setShowMatchCorrection(false)}
                onSearch={handleSearch}
                onConfirm={handleConfirmMatch}
                // searchStatus={searchStatus === 'loading' ? (searchLoading ? 'loading' : 'idle') : searchStatus}
                searchStatus={searchStatus}
                searchResults={searchResults}
                selectedItem={selectedMatch}
                onSelectItem={handleSelectMatch}
                placeholder="请输入匹配的影片名称"
                confirmButtonText="确定修正"
                errorMessage="搜索失败,请重试"
                emptyMessage="没搜索到相关结果"
                itemType="movie"
                onResetStatus={handleResetSearchStatus}
                keyField="media_id"
            />
            {!showMatchCorrection && (
                <div className={styles.scrollContainer}>
                    {/* 顶部背景和电影信息 */}
                    <div className={styles.headerSection} style={headerStyle}>
                        <div className={styles.posterOverlay}>
                            <NavigatorBar
                                right={rightSize}
                                backIconTheme="dark"
                                onBack={() => {
                                    // 返回到VideoDetails页面，将参数拼接到URL上
                                    const params = new URLSearchParams({
                                        classes: filmInfo?.classes || '',
                                        media_id: filmInfo?.media_id?.toString() || '0',
                                        lib_id: filmInfo?.lib_id?.toString() || '0'
                                    });
                                    
                                    history.push(`/filmAndTelevisionWall_app/all/videoDetails?${params.toString()}`);
                                }}
                            />
                        </div>
                        <div className={styles.movieInfo}>
                            <span style={{color:'#D8D8D8',fontSize:'13px'}}>{videoInfo.name}</span>
                            <h1 className={styles.title} style={{marginTop:'10px'}}>{videoInfo.title}</h1>
                            <div className={styles.metaInfo}>
                                {/* <span className={styles.score}>{videoInfo.score} 分</span> */}
                                {/* <span className={styles.divider}>/</span> */}
                                <span>{videoInfo.year}</span>
                                <span className={styles.divider}>/</span>
                                <span>{videoInfo.kind}</span>
                                <span className={styles.divider}>/</span>
                                <span>{videoInfo.category}</span>
                                <span className={styles.divider}>/</span>
                                <span>{formatDuration(videoInfo.duration)}</span>
                                
                            </div>
                            {/* <div className={styles.sourceInfo}>
                                来自 "{videoInfo.source}"
                            </div> */}
                        </div>
                    </div>

                    {/* 画质标签 */}
                    {filmInfo && (
                        <div className={styles.qualityTags}>
                            {/* 分辨率标签 - 只显示指定值 */}
                            {['4K', '蓝光', '1080P', '720P'].includes(filmInfo.resolution) && (
                                <Tag color="#D8D8D8" className={styles.qualityTag}>{filmInfo.resolution}</Tag>
                            )}
                            <Tag color="#D8D8D8" className={styles.qualityTag}>{filmInfo.hdr}</Tag>
                            <Tag color="#D8D8D8" className={styles.qualityTag}>{filmInfo.audio_codec}</Tag>
                        </div>
                    )}


                    {/* 版本选择器 */}

                    {/* <div className={styles.versionSelector}>
                        <Popover
                            style={{ '--arrow-size': '0px' } as React.CSSProperties}
                            className={styles.versionSelectorContent}
                            content={
                                <div className={styles.versionList}>
                                    {videoInfo?.versions?.map((version, index) => (
                                        <React.Fragment key={version.id}>
                                            <div
                                                className={`${styles.versionItem} ${selectedVersionId === version.id ? styles.versionItemSelected : ''}`}
                                                onClick={() => handleVersionSelect(version.id)}
                                            >
                                                <span className={styles.versionItemText}>{version.name}</span>
                                                {selectedVersionId === version.id && <CheckOutline className={styles.versionItemCheckIcon} />}
                                            </div>
                                            {index < videoInfo?.versions?.length - 1 && <Divider className={styles.versionDivider} />}
                                        </React.Fragment>
                                    ))}
                                </div>
                            }
                            trigger='click'
                            placement='bottom'
                            visible={popoverVisible}
                            onVisibleChange={setPopoverVisible}
                        >
                            <div className={styles.versionButton} onClick={() => setPopoverVisible(true)}>
                                <span className={styles.versionButtonText}>{selectedVersionName}</span>
                                <span className={styles.versionButtonIcon}><PreloadImage src={updon} alt="select" /></span>
                            </div>
                        </Popover>
                    </div> */}


                    {/* 操作按钮 */}
                    <div className={styles.actionButtons}>
                        <Button className={styles.playButton} color='primary' size='large' onClick={handlePlay}>
                            <PlayOutline /> 播放
                        </Button>
                        <Button className={styles.iconButton} onClick={handleDownload}>
                            <DownlandOutline color='#ffffff' fontSize={25} />
                        </Button>
                        <Button
                            className={styles.iconButton}
                            onClick={toggleFavorite}
                        >
                            {isFavorite ?
                                <HeartFill color='#FA311B' fontSize={25} /> :
                                <HeartOutline color='#ffffff' fontSize={25} />
                            }
                        </Button>
                        <Button className={styles.iconButton} onClick={toggleCheck}>
                            {isCheck ?
                                <CheckOutline color='#0080FF' fontSize={25} /> :
                                <CheckOutline color='#ffffff' fontSize={25} />
                            }
                        </Button>
                    </div>

                    {/* 文件信息 */}
                    <div className={styles.fileInfoSection}>
                        <h2 className={styles.sectionTitle}>文件信息</h2>
                        <div className={styles.fileInfoBox}>
                            <div className={styles.fileInfoItem}>
                                <div>
                                    <span className={styles.fileInfoLabel}>文件路径：</span>
                                    <span className={styles.fileInfoValue}>{CommonUtils.formatFilePath(videoInfo.fileInfo.path)}</span>
                                </div>
                            </div>
                            <div className={styles.fileInfoItem}>
                                <div>
                                    <span className={styles.fileInfoLabel}>文件大小：</span>
                                    <span className={styles.fileInfoValue}>{videoInfo.fileInfo.size}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
};

export default VideoDetails;
