import React, {
  useEffect,
  useState,
  forwardRef,
  useImperativeHandle,
  useRef,
} from "react";
import { List, Checkbox, Button, Toast } from "antd-mobile";
import styles from "./index.module.scss";
import { PreloadImage } from "@/components/Image";
import fileIcon from "@/Resources/nasDiskImg/file-icon.png";
import folderIcon from "@/Resources/nasDiskImg/file-icon.png";
import audioIcon from "@/Resources/nasDiskImg/musicIcon.png"; // 音频文件图标
import docIcon from "@/Resources/nasDiskImg/textIcon.png"; // 文档图标
import appIcon from "@/Resources/nasDiskImg/otherIcon.png"; // 应用图标
import btIcon from '@/Resources/nasDiskImg/btIcon.png'
import pdfIcon from "@/Resources/nasDiskImg/pdfIcon.png";
import zipIcon from "@/Resources/nasDiskImg/zipIcon.png";
import pptIcon from "@/Resources/nasDiskImg/pptIcon.png";
import wordIcon from "@/Resources/nasDiskImg/wordIcon.png";
import xlsIcon from "@/Resources/nasDiskImg/xlsIcon.png";
import textIcon from "@/Resources/nasDiskImg/textIcon.png";
import { getFileType, FileTypes } from "@/utils/fileTypeUtils";
import { modalShow } from "@/components/List";
import { useHistory, useLocation } from 'react-router-dom';
import { useRequest } from "ahooks";
import { downloadFromBaiduNetdisk, BaiduDownloadPathItem } from "@/api/nasDisk";
import { getPoolInfo } from "@/api/fatWall";

// 定义文件项类型
export type FileItemType = "file" | "folder";

// 定义缩略图对象结构
export interface ThumbsObject {
  icon?: string;
  url1?: string;
  url2?: string;
  url3?: string;
  [key: string]: string | undefined;
}

export interface FileItem {
  id: number;
  name: string;
  type: FileItemType;
  time: string;
  icon: string;
  size?: number; // 添加可选的文件大小属性
  children?: FileItem[];
  category?: number; // 文件类型：1 视频、2 音频、3 图片、4 文档、5 应用、6 其他、7 种子
  thumbs?: ThumbsObject; // 图片缩略图URL对象
}

export interface FileListProps {
  // 文件数据
  data: FileItem[];
  // 是否可编辑(选择文件)
  editable?: boolean;
  // 是否显示添加按钮
  showAddButton?: boolean;
  // 是否显示返回按钮
  showBackButton?: boolean;
  // 标题
  title?: string;
  // 点击文件项回调
  onItemClick?: (item: FileItem) => void;
  // 点击下载按钮回调
  onDownload?: (selectedIds: number[]) => void;
  // 点击添加按钮回调
  onAddClick?: () => void;
  // 点击返回按钮回调
  onBackClick?: () => void;
  // 自定义位置展示
  locationText?: string;
  // 自定义位置图标
  locationIcon?: string;
  // 编辑状态变化回调
  onEditStateChange?: (isEditing: boolean) => void;
  // 加载状态
  loading?: boolean;
  isVip?: boolean;
  // 新增: 外部传入的选中ID列表
  externalSelectedIds?: number[];
  // 新增: 选择项变化回调
  onSelectionChange?: (ids: number[]) => void;
  // 当前路径
  currentPath?: string;
}

// 定义组件引用类型
export interface FileListRef {
  setIsEditing: (value: boolean) => void;
  resetSelection: () => void; // 重置选中状态
  getSelectedIds: () => number[]; // 获取当前选中ID列表
}

const FileList = forwardRef<FileListRef, FileListProps>(
  (
    {
      data = [],
      editable = true,
      showAddButton = true,
      showBackButton = true,
      title = "百度网盘",
      onItemClick,
      onDownload,
      onAddClick,
      onBackClick,
      locationText = "内部存储01>百度网盘",
      locationIcon = "",
      onEditStateChange,
      loading = false,
      isVip = false,
      externalSelectedIds,
      onSelectionChange,
      currentPath = "/"
    },
    ref
  ) => {
    const history = useHistory();
    const location = useLocation<{
      downloadPath?: string;
      downloadDisplayPath?: string;
      isVip?: boolean;
      isEditing?: boolean;
      selectedIds?: number[];
      downloadCompleted?: boolean;
      pendingLongPress?: number;
    }>();
    
    // 编辑态
    const [isEditing, setIsEditing] = useState<boolean>(false);
    const [selectedIds, setSelectedIds] = useState<number[]>([]);
    // 下载位置显示文本
    const [downloadLocation, setDownloadLocation] = useState<string>(locationText);
    // 添加一个标志位，用于标记是否正在处理长按事件
    const [isLongPressing, setIsLongPressing] = useState<boolean>(false);
    // 添加一个标志位，用于记录已处理的状态，避免重复处理
    const processedStateRef = useRef<string | null>(null);
    
    // 存储池和WebDAV配置信息
    const [defaultDownloadPath, setDefaultDownloadPath] = useState<string>('');
    const [, setWebDAVConfig] = useState<{alias_root?: string} | null>(null);

    // 同步外部传入的选中ID列表
    useEffect(() => {
      if (externalSelectedIds !== undefined) {
        setSelectedIds(externalSelectedIds);
      }
    }, [externalSelectedIds]);

    // 触摸事件相关状态
    const containerRef = useRef<HTMLDivElement>(null);
    const [touchStartX, setTouchStartX] = useState(0);
    const [touchEndX, setTouchEndX] = useState(0);
    
    // 获取存储池信息
    const { run: fetchPoolInfo } = useRequest(getPoolInfo, {
      manual: true,
      onSuccess: (response) => {
        if (response.code === 0 && response.data) {
          // 保存WebDAV配置
          if (response.data.webDAV) {
            setWebDAVConfig(response.data.webDAV);
          }

          // 获取第一个存储池的顶层目录作为默认下载路径
          if (response.data.internal_pool.length > 0) {
            const firstPool = response.data.internal_pool[0];

            if (response.data.webDAV?.alias_root) {
              const dataDir = firstPool.data_dir.endsWith("/")
                ? firstPool.data_dir.slice(0, -1)
                : firstPool.data_dir;
              const aliasRoot = response.data.webDAV.alias_root;
              
              // 设置默认的下载路径
              const defaultPath = `${aliasRoot}${dataDir}/百度网盘`;
              setDefaultDownloadPath(defaultPath);
            }
          }
        }
      },
      onError: (error) => {
        console.error("获取存储池信息失败：", error);
      }
    });
    
    // 组件挂载时获取存储池信息
    useEffect(() => {
      fetchPoolInfo({});
    }, [fetchPoolInfo]);

    // 下载请求
    const { run: runDownload, loading: downloadLoading } = useRequest(
      (params: {
        remotePath: BaiduDownloadPathItem[];
        localPath: string;
        autotask: number;
      }) => {
        return downloadFromBaiduNetdisk({
          action: "download",
          autotask: params.autotask,
          remotepath: params.remotePath,
          localpath: params.localPath
        });
      },
      {
        manual: true,
        onSuccess: (result) => {
          if (result.code === 0) {
            Toast.show({
              icon: 'success',
              content: '下载任务添加成功',
            });
            
            // 下载后退出编辑态
            setIsEditing(false);
            setSelectedIds([]);
            
            // 如果有下载路径，则跳转回主页面并保持在网盘文件标签页
            if (location.state?.downloadPath) {
              // 添加任务数量
              history.replace({
                pathname: location.pathname,
                state: {
                  ...location.state,
                  newTaskCount: selectedIds.length,
                  isEditing: false,
                  activeTab: 'diskfiles',
                }
              });
            }
            
            // 通知父组件下载成功
            if (onDownload) {
              onDownload(selectedIds);
            }
          } else {
            Toast.show({
              icon: 'fail',
              content: `下载任务添加失败: ${result.result}`,
            });
            
            if (result.failed_paths && result.failed_paths.length > 0) {
              console.error('下载失败的路径:', result.failed_paths);
            }
          }
        },
        onError: (error) => {
          Toast.show({
            icon: 'fail',
            content: '下载请求出错，请重试',
          });
          console.error('下载请求出错:', error);
        }
      }
    );

    // 检查location中是否有下载路径信息和编辑状态
    useEffect(() => {
      // 如果正在长按，不处理location状态变化
      if (isLongPressing) {
        return;
      }

      // 使用stringified state作为缓存键，避免重复处理相同的状态
      const stateKey = JSON.stringify(location.state);
      if (stateKey === processedStateRef.current) {
        return;
      }
      processedStateRef.current = stateKey;

      // 如果从Downlocation返回，恢复编辑状态和选中项
      if (location.state) {
        
        // 先恢复已选中的文件，确保无论后续逻辑如何，选中状态都会被保留
        if (location.state.selectedIds && location.state.selectedIds.length > 0) {
          setSelectedIds(location.state.selectedIds);
        }
        
        if (location.state.downloadDisplayPath) {
          setDownloadLocation(location.state.downloadDisplayPath);
        }
        
        // 只有当不是下载完成状态时，才恢复编辑状态
        if (location.state.isEditing !== undefined) {
          setIsEditing(location.state.isEditing);
          
          // 通知父组件编辑状态已恢复
          if (onEditStateChange) {
            onEditStateChange(location.state.isEditing);
          }
        }
      }
    }, [location.state, onEditStateChange, isLongPressing]);

    // 暴露方法给父组件
    useImperativeHandle(ref, () => ({
      setIsEditing: (value: boolean) => {
        setIsEditing(value);
        // 退出编辑态时清空选中项，但如果有location.state中的selectedIds则不清空
        if (!value && (!location.state?.selectedIds || location.state.selectedIds.length === 0)) {
          setSelectedIds([]);
        }
      },
      resetSelection: () => {
        setSelectedIds([]);
      },
      getSelectedIds: () => selectedIds
    }), [selectedIds, location.state?.selectedIds]);

    // 通知父组件编辑状态变化
    useEffect(() => {
      if (onEditStateChange) {
        onEditStateChange(isEditing);
      }
      
      // 当编辑状态变化时，如果退出编辑态，清空选中项，但如果有location.state中的selectedIds则不清空
      if (!isEditing && (!location.state?.selectedIds || location.state.selectedIds.length === 0)) {
        setSelectedIds([]);
      }
    }, [isEditing, onEditStateChange, location.state?.selectedIds]);

    // 长按计时器
    const [longPressTimer, setLongPressTimer] = useState<NodeJS.Timeout | null>(
      null
    );

    // 处理容器触摸开始事件
    const handleContainerTouchStart = (e: React.TouchEvent) => {
      setTouchStartX(e.touches[0].clientX);
    };

    // 处理容器触摸结束事件
    const handleContainerTouchEnd = (e: React.TouchEvent) => {
      setTouchEndX(e.changedTouches[0].clientX);
    };

    // 监听触摸事件，实现右滑返回
    useEffect(() => {
      const handleSwipe = () => {
        // 右滑的距离阈值（像素）
        const threshold = 100;

        // 如果是右滑且滑动距离超过阈值
        if (touchEndX - touchStartX > threshold) {
          // 如果有返回回调，调用它
          if (onBackClick) {
            onBackClick();
          }
        }
      };

      // 只有在触摸结束且有触摸开始记录时才处理滑动
      if (touchEndX > 0 && touchStartX > 0) {
        handleSwipe();
        // 重置触摸状态
        setTouchStartX(0);
        setTouchEndX(0);
      }
    }, [touchEndX, touchStartX, onBackClick]);

    // 长按开始
    const handleItemTouchStart = (id: number, e: React.TouchEvent) => {
      if (!editable) return;
      e.stopPropagation(); // 阻止事件冒泡
      // 标记正在长按
      setIsLongPressing(true);
      // 无论是否有downloadCompleted标志，都直接设置定时器
      startLongPressTimer(id);
    };
    
    // 监听路由状态变化，处理待处理的长按事件
    useEffect(() => {
      // 如果有待处理的长按事件，且不是在长按状态中
      if (location.state?.pendingLongPress && !longPressTimer) {
        startLongPressTimer(location.state.pendingLongPress);
        
        // 清除待处理标记，避免重复处理
        history.replace({
          pathname: location.pathname,
          state: {
            ...location.state,
            pendingLongPress: undefined
          }
        });
      }
    // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [location.state?.pendingLongPress, longPressTimer, history, location.pathname]);
    
    // 抽取设置长按定时器的逻辑为单独的函数
    const startLongPressTimer = (id: number) => {
      // 清除可能存在的旧定时器
      if (longPressTimer) {
        clearTimeout(longPressTimer);
      }
      
      const timer = setTimeout(() => {
        // 直接设置编辑态，不通过路由状态
        setIsEditing(true);
        // 长按完成后，重置长按标志
        setIsLongPressing(false);
      }, 500); // 长按时间阈值为 500ms
      
      setLongPressTimer(timer);
    };

    // 长按结束
    const handleItemTouchEnd = (e: React.TouchEvent) => {
      e.stopPropagation(); // 阻止事件冒泡
      if (longPressTimer) {
        clearTimeout(longPressTimer);
        setLongPressTimer(null);
      }
      
      // 如果长按被取消，也需要重置长按标志
      setIsLongPressing(false);
    };

    // 点击复选框事件处理
    const handleCheckboxChange = (id: number, checked: boolean) => {
      let newSelectedIds;
      if (checked) {
        newSelectedIds = [...selectedIds, id]; // 添加到选中列表
      } else {
        newSelectedIds = selectedIds.filter((item) => item !== id); // 从选中列表移除
      }
      setSelectedIds(newSelectedIds);

      // 通知父组件选中状态变化
      if (onSelectionChange) {
        onSelectionChange(newSelectedIds);
      }
    };

    // 处理点击事件
    const handleItemClick = (item: FileItem) => {
      if (isEditing) {
        // 编辑态下点击切换选中状态
        handleCheckboxChange(item.id, !selectedIds.includes(item.id));
      } else {
        // 非编辑态下直接调用父组件的点击回调
        if (onItemClick) {
          onItemClick(item);
        }
      }
    };

    // 处理下载按钮点击事件
    const handleDownload = () => {
      if (selectedIds.length === 0) {
        return;
      }

      // 获取选中的文件和文件夹
      const selectedFiles = data.filter(item => selectedIds.includes(item.id));
      
      // 准备下载参数
      const remotePath: BaiduDownloadPathItem[] = selectedFiles.map(item => ({
        type: item.type === 'folder' ? 'directory' : 'file',
        path: currentPath === '/' 
          ? `/${item.name}` 
          : `${currentPath}/${item.name}`
      }));
      
      // 使用location.state中的下载路径，或者使用从getPoolInfo获取的默认路径
      const localPath = location.state?.downloadPath || defaultDownloadPath;
      
      // 调用下载
      runDownload({
        remotePath,
        localPath,
        autotask: 0 // 非自动任务
      });
    };

    // 显示会员权益弹窗或导航到下载位置选择页面
    const showVipModal = () => {
      if(!selectedIds.length) return;
      
      if(isVip || location.state?.isVip){
        // 导航到下载位置选择页面，并传递当前页面路径、编辑状态和已选中的文件
        history.push({
          pathname: '/baiduNetdisk_app/downlocation',
          state: { 
            from: location.pathname,
            isVip: isVip || location.state?.isVip,
            isEditing: isEditing,
            selectedIds: selectedIds,
            activeTab: 'diskfiles' // 确保返回时保持在网盘文件标签页
          }
        });
        return;
      }
      
      modalShow(
        "会员权益",
        "该功能为网盘NAS会员权益，是否要开启？",
        (m) => {
          // 确认按钮点击
          m.destroy();
         history.push(`/baiduNetdisk_app/members`);
        },
        () => {
          // 取消按钮点击
        },
        false,
        {
          position: 'bottom',
          okBtnText: '开通会员',
          cancelBtnText: '取消',
          okBtnStyle: { backgroundColor: '#402C00', color: '#E2AE1E' },
          cancelBtnStyle: { backgroundColor: '#F0F0F0', color: '#000' }
        }
      );
    };

    // 监听编辑态变化
    useEffect(() => {
      // 进入编辑态时重置选中状态，但如果有location.state中的selectedIds则不重置
      if (isEditing && !location.state?.selectedIds?.length) {
        setSelectedIds([]);
      }
    }, [isEditing, location.state?.selectedIds]);

    // 获取文件图标的函数
    const getFileIcon = (file: FileItem) => {
      if (file.type === "folder") {
        return folderIcon; // 使用文件夹图标
      }

      // 如果是图片且有缩略图，使用缩略图
      if ((file.category === 3 || file.category===1) && file.thumbs) {
        // 优先使用icon，如果没有则按优先级依次选择url1、url2、url3
        return file.thumbs.icon || file.thumbs.url1 || file.thumbs.url2 || file.thumbs.url3 || fileIcon;
      }

      // 根据文件扩展名获取更精确的图标
      const fileType = getFileType(file.name, false);

      // 根据文件类型返回对应图标
      switch (fileType) {
        case FileTypes.PDF:
          return pdfIcon;
        case FileTypes.ZIP:
          return zipIcon;
        case FileTypes.WORD:
          return wordIcon;
        case FileTypes.EXCEL:
          return xlsIcon;
        case FileTypes.PPT:
          return pptIcon;
        case FileTypes.TEXT:
          return textIcon;
        case FileTypes.AUDIO:
          return audioIcon;
        default:
          // 对于其他类型，保持原有的category逻辑作为后备
          switch (file.category) {
            case 2:
              return audioIcon; // 音频图标
            case 4:
              return docIcon; // 文档图标
            case 5:
              return appIcon; // 应用图标
            case 7:
              return btIcon; // 种子文件使用默认图标
            default:
              return fileIcon; // 默认图标
          }
      }
    };

    return (
      <div
        className={styles.ListContainer}
        ref={containerRef}
        onTouchStart={handleContainerTouchStart}
        onTouchEnd={handleContainerTouchEnd}
      >
        <List>
          {data.map((file) => (
            <List.Item
              key={file.id}
              onClick={() => handleItemClick(file)}
              arrow={!isEditing && file.type === "folder"}
              extra={
                isEditing &&
                editable && (
                  <Checkbox
                    checked={selectedIds.includes(file.id)}
                    onChange={(checked) => {
                      handleCheckboxChange(file.id, checked);
                    }}
                    onClick={(e) => e.stopPropagation()} // 阻止事件冒泡
                  />
                )
              }
            >
              <div
                className={styles.fileItem}
                onTouchStart={(e) => handleItemTouchStart(file.id, e)} // 长按开始
                onTouchEnd={handleItemTouchEnd} // 长按结束
              >
                <div className={styles.fileIcon}>
                  <PreloadImage
                    src={getFileIcon(file)}
                    alt=""
                    style={{ width: 40, height: 40, borderRadius: 5 }}
                  />
                </div>

                <div className={styles.fileInfo}>
                  <div className={styles.fileName}>{file.name}</div>
                  <div className={styles.fileTime}>{file.time}</div>
                </div>
              </div>
            </List.Item>
          ))}
        </List>

        {isEditing && editable && (
          <div className={styles.footerButtons}>
            {!isVip && !location.state?.isVip && (<div className={styles.vipTip}>VIP专享</div>)}
            <Button 
              block 
              color="primary" 
              className={styles.locationButton}
              onClick={showVipModal}
            >
              <span className={styles.locatext}>
                下载到:<span >{downloadLocation}</span>
              </span>
            </Button>
            <Button
              block
              color="primary"
              className={styles.downloadButton}
              onClick={handleDownload}
              disabled={selectedIds.length === 0 || downloadLoading}
              loading={downloadLoading}
            >
              {downloadLoading ? '下载中...' : `下载 (${selectedIds.length})`}
            </Button>
          </div>
        )}
      </div>
    );
  }
);

export default FileList;
