import { useCallback, useMemo, useState } from 'react';
import "./MoreOptPlugin.css";
import moreOpt from "@/Resources/player/moreOpt.png";
// import { usePlayerDownload } from '@/components/CameraPlayer/utils/utils';
import Player from 'xgplayer/es/player';
import { PreloadImage } from '@/components/Image';
import share from '@/Resources/icon/share.png';
import del from '@/Resources/icon/delete.png';
import download from '@/Resources/icon/download.png';
import share_dark from '@/Resources/icon/share_white.png';
import del_dark from '@/Resources/icon/delete_white.png';
import download_dark from '@/Resources/icon/download_white.png';
import detail_dark from '@/Resources/icon/detail_dark.png';
import detail from '@/Resources/icon/detail.png';
import { callTaskCenter, downloadAndSaveWithPlayer, downloadAndShareWithPlayer } from '@/api/cameraPlayer';
import { useTheme } from '@/utils/themeDetector';
import List, { IListData, modalShow } from '@/components/List';
import { Toast } from '@/components/Toast/manager';
import { move2trashbin } from '@/api/fatWall';
import { IMonitorPlayerOptions } from '../../MonitorPlayer/MonitorPlayer';
import { getSystemType } from '@/utils/DeviceType';
interface IMoreOptPlugin {
  deviceType: 'pc' | 'mobile'
  player: React.MutableRefObject<Player | null>
  isFull: boolean // 是否全屏播放
  isDashboard?: boolean
  options?: IMonitorPlayerOptions
}

interface IMoreOptList {
  label: string,
  name: string,
  color: string,
  icon: string
}

const MoreOptPlugin = (props: IMoreOptPlugin) => {
  const { deviceType, player, isFull, isDashboard, options } = props;
  const [isHovered, setIsHovered] = useState<boolean>(false);
  const isAndroid = getSystemType() === 'android';
  // const isIos = getSystemType() === 'ios';

  const onShare = useCallback(async () => {
    if (deviceType === 'pc') {
      try {
        await navigator.clipboard.writeText(player.current?.src);
        Toast.show('复制成功');
      } catch (e) {
        console.log('shareErr', e);
      }
      return;
    }
    await downloadAndShareWithPlayer(player.current?.src, (res) => {
      if (res && res.code === 0) {
        setIsHovered(false);
      }
    })
  }, [deviceType, player])

  const onDelete = useCallback(async () => {
    const res = await move2trashbin({ path: [player.current?.src] }).catch((e) => { Toast.show('删除失败，请稍后再试'); });
    if (res && res.code === 0) {
      Toast.show('正在删除');
      setIsHovered(false);
      return;
    }
  }, [player])

  const onDownload = useCallback(async () => {
    downloadAndSaveWithPlayer(player.current?.src, (res) => {
      if (res && res.code === 0) {
        setIsHovered(false);
        Toast.show('正在下载');
      }
    })

    if (deviceType) {
      await callTaskCenter();
    }
  }, [deviceType, player])

  const detailList: IListData[] = useMemo(() => {
    return [
      { key: 'name', type: 'text', label: '文件名称', value: '小米C300' },
      { key: 'name1', type: 'text', label: '文件格式', value: 'mp4' },
      { key: 'nam2', type: 'text', label: '视频格式', value: 'H.265' },
      { key: 'name3', type: 'text', label: '大小', value: '109MB' },
      { key: 'name4', type: 'text', label: '位置', value: '/存储池1/监控中心' },
      { key: 'name5', type: 'text', label: '录制时间', value: '今天18:23' },
      { key: 'type', type: 'text', label: '标签', value: '' },
    ]
  }, [])

  const detailCallback = useCallback(() => {
    modalShow('查看详情', <div className={'moreOpt_lookBackDetail_modal_container'}>
      <List dataSource={detailList}></List>
    </div>, (m) => m.destroy(), () => null, true)
  }, [detailList])

  const moreEvent: { [key: string]: () => void } = useMemo(() => {
    return {
      share: onShare,
      delete: onDelete,
      download: onDownload,
      detail: detailCallback
    }
  }, [detailCallback, onDelete, onDownload, onShare])

  const selectEvent = useCallback((eventName: string) => {
    // 如果是自定义操作，直接调用自定义的onClick方法
    if (options?.customOperations && options.customOperations.length > 0) {
      const customOp = options.customOperations.find(op => op.name === eventName);
      if (customOp) {
        customOp.onClick();
        setIsHovered(false);
        return;
      }
    }
    
    // 否则使用默认的操作
    if (moreEvent[eventName]) {
      moreEvent[eventName]();
    }
  }, [moreEvent, options?.customOperations])

  const { isDarkMode } = useTheme();

  const moreOptList: IMoreOptList[] = useMemo(() => {
    // 如果有自定义操作按钮，使用自定义的；否则使用默认的
    if (options?.customOperations && options.customOperations.length > 0) {
      return options.customOperations.map(op => ({
        label: op.label,
        name: op.name,
        color: 'var(--text-color)',
        icon: op.icon
      }));
    }
    return [
      { label: "下载", name: 'download', color: 'var(--text-color)', icon: isDarkMode ? download_dark : download },
      { label: "分享", name: 'share', color: 'var(--text-color)', icon: isDarkMode ? share_dark : share },
      { label: '查看详情', name: 'detail', color: 'var(--text-color)', icon: isDarkMode ? detail_dark : detail },
      { label: "删除", name: 'delete', color: 'red', icon: isDarkMode ? del_dark : del },
    ]
  }, [isDarkMode, options?.customOperations])

  return (
    <div className="moreOpt-container" onTouchStart={() => setIsHovered(true)} onMouseEnter={() => setIsHovered(true)} onMouseLeave={() => setIsHovered(false)}>
      <PreloadImage className={`iconBtn_container_img ${isFull ? 'full' : 'notFull'} ${options?.type}  ${isAndroid ? 'android' : ''} ${deviceType} ${isDashboard && !isFull ? 'dashboard' : ''}`} src={moreOpt} alt="moreOpt" />
      <div className={`moreOpt-select-container ${deviceType}`} style={{ display: isHovered ? 'flex' : 'none', zIndex: isHovered ? 100 : 1 }}>
        {
          moreOptList.map((it: IMoreOptList) => {
            return <span key={it.name} style={{ color: it.color }} className={`moreOpt-select-span ${deviceType}`} onClick={() => selectEvent(it.name)}>
              <PreloadImage className={`moreOpt-select-span-img ${deviceType}`} src={it.icon} alt='icon' />
              {it.label}
            </span>
          })
        }
      </div>
    </div>
  );
};

export default MoreOptPlugin;