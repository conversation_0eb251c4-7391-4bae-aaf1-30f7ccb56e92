import { useState } from 'react';
import { Button, Toast } from 'antd-mobile';
import { useHistory } from 'react-router-dom';
import styles from './index.module.scss';
import NavigatorBar from '@/components/NavBar';
import { useTheme } from '@/utils/themeDetector';

const Connect = () => {
    const history = useHistory();
    const [loginCode, setLoginCode] = useState('536541'); // 默认6位数字
    const { isDarkMode } = useTheme();

    const handleRefreshLoginCode = () => {
        // 生成新的6位数字登录码
        const newCode = Math.floor(100000 + Math.random() * 900000).toString();
        setLoginCode(newCode);
        Toast.show('登录码已刷新');
    };

    return (
        <div className={styles.container}>
            <NavigatorBar backIconTheme={isDarkMode ? 'dark' : 'light'} onBack={history.goBack} />

            <div className={styles.content}>
                {/* 第一步 */}
                <div className={styles.step}>
                    <h2 className={styles.stepTitle}>第一步</h2>
                    <p className={styles.stepDescription}>
                        将电脑或平板与带中枢功能的设备连接至同一网络
                    </p>

                    <div className={styles.deviceConnection}>
                        <div className={styles.deviceGroup}>
                            <div className={styles.deviceIcon}>
                                <div className={styles.computerIcon}>💻</div>
                            </div>
                            <p className={styles.deviceLabel}>电脑 | 平板 | 中枢设备</p>
                        </div>

                        <div className={styles.connectionLine}></div>

                        <div className={styles.deviceGroup}>
                            <div className={styles.deviceIcon}>
                                <div className={styles.routerIcon}>📶</div>
                            </div>
                            <p className={styles.deviceLabel}>路由器</p>
                        </div>
                    </div>
                </div>

                {/* 第二步 */}
                <div className={styles.step}>
                    <h2 className={styles.stepTitle}>第二步</h2>
                    <p className={styles.stepDescription}>
                        打开浏览器，在浏览器中输入中枢网关的IP地址并访问
                    </p>

                    <div className={styles.ipDisplay}>
                        **************:8086
                    </div>
                </div>

                {/* 第三步 */}
                <div className={styles.step}>
                    <h2 className={styles.stepTitle}>第三步</h2>
                    <p className={styles.stepDescription}>
                        把六位数字填入网页中，并点击进入
                    </p>

                    <div className={styles.codeDisplay}>
                        {loginCode.split('').map((digit, index) => (
                            <span key={index} className={styles.digit}>
                                {digit}
                            </span>
                        ))}
                    </div>
                </div>

                {/* 重新获取登录码按钮 */}
                <div className={styles.buttonSection}>
                    <Button
                        color="primary"
                        size="large"
                        onClick={handleRefreshLoginCode}
                        className={styles.refreshButton}
                    >
                        重新获取登录码
                    </Button>
                </div>
            </div>
        </div>
    );
};

export default Connect;