import React, { useCallback, useEffect, useMemo, useRef } from 'react'
import { endOfDay, format, startOfDay } from "date-fns";
import { zhCN } from "date-fns/locale";
import dateSelect from '@/Resources/player/dateSelect.png';
import { useState } from "react";
import "./EventOperate.css";
import DatePicker from '../DatePicker/DatePicker';
import { PreloadImage } from '@/components/Image';
import { px2rem } from '../TimeAxis/TimeAxis';
import Modal from '@/components/Modal';
import { IEventListCard } from '../EventList/EventListCard';
import EventList from '../EventList/EventList';
import { eventDefinition, EventLookBackType } from '@/components/CameraPlayer/constants';
import { getDeviceType } from '@/utils/DeviceType';
import { IEventVideo } from '@/pages/IPC/IPC_APP/CameraDetail';
import styles from "@/pages/IPC/IPC_APP/CameraDetail/index.module.scss";
import { useHistory, useRouteMatch } from 'react-router-dom';
import { IlLookBackData } from '../plugin/ControlPlugin/PlayerControl';
import { useInViewport, useRequest, useUpdateEffect } from 'ahooks';
import { getVideoRecord } from '@/api/ipc';
import { splitURL } from '../MonitorPlayer/MonitorPlayer';

interface IIdentifyUser {
  uuid: string,
  name: string,
  profile_pic: string,
}

const EventOperate: React.FC<{
  controlEventFilter: (v: any) => void; // 控制事件过滤的回调函数
  selectedDate: Date; // 当前选中的日期时间
  setSelectedDate: (v: Date) => void;
  camera_lens: string[];
  style?: React.CSSProperties;
  children?: React.ReactNode, // 包裹的子元素
  eventData: IEventVideo[], // 事件数据
  needEvent?: boolean, // 是否需要显示事件
  currentEventKey: string
  parentLoaderRef?: React.RefObject<HTMLDivElement>
  parentLoaderCallback?: (v: boolean) => void // 父级在滚动到底部时，控制是否需要更多数据
}> = (props) => {
  const { controlEventFilter, selectedDate, setSelectedDate, style, camera_lens, children, eventData, needEvent = false, currentEventKey, parentLoaderRef, parentLoaderCallback } = props;
  const [datePickerIsShow, setDatePickerIsShow] = useState<boolean>(false);
  const [usersModalShow, setUsersModalShow] = useState<boolean>(false);
  const [eventFilterModalShow, setEventFilterModalShow] = useState<boolean>(false);
  const [identifyUsers, setIdentifyUsers] = useState<IIdentifyUser[]>([]);
  const [eventFilter, setEventFilter] = useState<EventLookBackType>('all');

  const eventParamsRef = useRef<EventLookBackType>('all');
  const pageRef = useRef<{ size: number, token: string }>({ size: 10, token: '' }); // 0的话标识查询全部

  const history = useHistory();
  const { path } = useRouteMatch();
  const [eventList, setEventList] = useState<(IEventListCard & IEventVideo)[]>([]); // 事件列表数据

  const { runAsync: videoRecordRun } = useRequest(getVideoRecord, { manual: true });

  // 控制是否自动加载
  const loaderRef = useRef<HTMLDivElement>(null);
  const [hasMore, setHasMore] = useState(false);

  const userFilter: React.ReactNode = useMemo(() => {
    const users = identifyUsers.slice(0, 4);
    return users.map((item: IIdentifyUser, index: number) => (
      <div className='identify_users_content' key={item.uuid} style={{ left: `${index * 100 / users.length}%`, zIndex: users.length - index }}>
        {item.profile_pic ? <PreloadImage style={{ height: px2rem(28) }} src={item.profile_pic} alt='user_avatar' /> : <></>}
      </div>
    ))
  }, [identifyUsers])

  const identifyErrorCallback = useCallback(async (item: IIdentifyUser) => {
    // todo
  }, [])

  const userModal = useMemo(() => {
    const users: IEventListCard[] = identifyUsers.map((item) => {
      const hasName: boolean = item.name && item.name !== '' ? true : false;
      return {
        icon: item.profile_pic, title: hasName ? item.name : '陌生人', subtitle: hasName ? '查看详情' : '', rightOpt: { title: '识别报错', type: 'status', callback: identifyErrorCallback }, ...item
      }
    });
    return <div className='event_userModal_container'><EventList data={users} callback={(item: any) => history.push(`${path}/faceRecognition/faceDetail/${item.uuid}`)} /></div>
  }, [history, identifyErrorCallback, identifyUsers, path])

  const eventFilterModal = useMemo(() => {
    return <div className='eventFilterModal_container'>
      {
        Object.keys(eventDefinition).map((key: any) => (
          <div className={`eventFilterModal_content ${eventFilter === key ? 'filter_selected' : ''}`} key={key} onClick={() => { setEventFilter(key); setEventFilterModalShow(false) }}>
            <span><PreloadImage src={eventDefinition[key].icon} /></span>
            <span>{eventDefinition[key].label}</span>
          </div>
        ))
      }
    </div>
  }, [eventFilter])

  // 获取当日事件数据
  const getTodayEventData = useCallback(async () => {
    if (camera_lens.length === 0) return;
    const t: EventLookBackType[] = eventParamsRef.current === 'all' ? ['human', 'fire', 'pet',] : [eventParamsRef.current];
    const params = {
      page: pageRef.current,
      options: {
        option: ['event_name', 'time', 'camera_lens'],
        event_name: t,
        camera_lens: camera_lens,
        time: {
          start: startOfDay(selectedDate).getTime(),
          end: endOfDay(selectedDate).getTime()
        }
      }
    }

    const res = await videoRecordRun(params).then().catch((e) => console.log(e));
    if (res && res.code === 0 && res.data) {
      // 保存eventData
      controlEventFilter((p: IEventVideo[]) => {
        return [...p, ...res.data.videos.map((it, index) => { return { ...it, id: it.camera_lens + index } })]
      });
      pageRef.current = { ...pageRef.current, token: res.data.page.token }

      // 根据列表返回的数量判断是否还有更多数据
      if (res.data.videos.length < pageRef.current.size) {
        setHasMore(false);
        parentLoaderCallback && parentLoaderCallback(false); // 父级在滚动到底部时，控制是否需要更多数据
      } else {
        setHasMore(true);
        parentLoaderCallback && parentLoaderCallback(true);
      }
    }
  }, [camera_lens, controlEventFilter, parentLoaderCallback, selectedDate, videoRecordRun])

  // 获取事件列表数据
  useUpdateEffect(() => {
    const fList = eventData.filter((it) => it.event_name !== '');
    const temp: any[] = [];
    fList.forEach(async (it) => {
      temp.push({
        id: it.id,
        icon: eventDefinition[it.event_name].icon,
        title: eventDefinition[it.event_name].label,
        subtitle: format(new Date(Number(it.time)), 'HH:mm', { locale: zhCN }),
        poster: `${it.cover_file}/original`,
        name: it.event_name,
        file: it.file,
        urls: eventData.filter((item) => item.event_name === it.event_name).map((it) => it.file)
      })
    })

    setEventList(temp);
  }, [eventData])

  const lookBackDetail = useCallback((item) => {
    const lookBackData: IlLookBackData = {
      type: 'movie',
      url: item.file ? splitURL(item.file) : '',
      eventOptions: {
        type: item.name ? item.name : 'all',
        label: `${item.subtitle} ${item.title}`,
        urls: item.urls || []
      }
    }
    history.push({
      pathname: '/cameraManagement_app/cameraDetail/lookBackDetail', state: {
        lookBackData: lookBackData
      }
    })
  }, [history])

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(parentLoaderRef || loaderRef, {
    threshold: 0.2,
  })

  useUpdateEffect(() => {
    if (inViewport) {
      getTodayEventData();
    }
  }, [inViewport]);

  // 事件筛选后设置ref
  useUpdateEffect(() => {
    if (eventFilter) {
      eventParamsRef.current = eventFilter;
    }
  }, [eventFilter, getTodayEventData]);

  useUpdateEffect(() => {
    if (currentEventKey === '') {
      setIdentifyUsers([]); // 重置识别用户列表
      return;
    }

    // 查找当前事件对应的 face_info 信息，并将其设置为识别用户列表
    const event = eventData.find((item) => item.id === currentEventKey);

    if (event && event.face_info && event.face_info.length > 0) {
      setIdentifyUsers(event.face_info);
    }
  }, [currentEventKey, eventData])

  useEffect(() => {
    getTodayEventData();
  }, [getTodayEventData])

  const deviceType = getDeviceType(); //mobile是0 pc是1 所以直接以true false判断

  return (
    <>
      <div className="cameraPlayer_eventOperate" style={{ ...style }}>
        <div className="cameraPlayer_eventOperate_left" onClick={() => setDatePickerIsShow(true)}>
          <span>{`${format(selectedDate, 'M月dd日', { locale: zhCN })} ${selectedDate.getDate() === new Date().getDate() ? '今天' : ''}`}</span>
          <PreloadImage style={{ height: px2rem(20) }} src={dateSelect} alt="dateSelect" />
        </div>
        <div className="cameraPlayer_eventOperate_center">
          {
            identifyUsers.length > 0 && (
              <div className='identify_users_container' style={{ width: `${px2rem(60)}` }} onClick={() => setUsersModalShow(true)}>
                {userFilter}
              </div>
            )
          }
        </div>
        <div className="cameraPlayer_eventOperate_right" onClick={() => setEventFilterModalShow(true)}>
          <div className='cameraPlayer_eventOperate_right_eventFilter'>
            <PreloadImage style={{ height: px2rem(18) }} src={eventDefinition[eventFilter].valueIcon} alt='eventFilter' />
          </div>
        </div>
      </div>
      {
        children && (<>
          <>{children}</>
          {
            needEvent && (
              <div className={styles.cameraPlayer_eventList}>
                <EventList data={eventList} callback={lookBackDetail} title="今日片段" needHeader={true} />
                {hasMore && (<div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>)}
              </div >
            )
          }
        </>
        )
      }
      <DatePicker isShow={datePickerIsShow} onCancel={() => setDatePickerIsShow(false)} onSelect={setSelectedDate} camera_lens={camera_lens} />
      <Modal contentStyle={{ minWidth: px2rem(360), height: px2rem(320) }} isShow={usersModalShow} content={userModal} onCancel={() => setUsersModalShow(false)} position={deviceType ? 'normal' : 'bottom'} width='100%' />
      <Modal contentStyle={{ minWidth: px2rem(360), height: px2rem(320) }} isShow={eventFilterModalShow} content={eventFilterModal} onCancel={() => setEventFilterModalShow(false)} position={deviceType ? 'normal' : 'bottom'} width='100%' />
    </>
  )
}

export default EventOperate