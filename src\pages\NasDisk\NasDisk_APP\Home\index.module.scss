.taskContent {
  margin-top: 7px;
  font-size: 10px;
  padding: 6px 8px;
  border-radius: 10px;
  background: linear-gradient(to right, #b9b9b9 60%, #d9d9d9 50%);
  color: var(--text-color);
}

.title {
  font-size: 32px;
  font-weight: 400;
  color: var(--title-color);
  padding: 0 16px;
}

.fileTab {
  background: var(--home-background-color);
  margin: 12px;
  border-radius: 16px;
  :global{
    .adm-capsule-tabs-tab-active{
      background-color: var(--background-color);
      color: var(--title-color) !important;
    }
    .adm-capsule-tabs-tab{
      color: var(--subtitle-text-color);
    }
  }
}

.fileTab :global(.adm-capsule-tabs-header) {
  border-bottom: none;
  padding: 6px 10px;
}

.fileTab :global(.adm-capsule-tabs-tab-list) :global(.adm-badge-wrapper) {
  padding: 5px 0;
}

.fileTab :global(.adm-badge) {
  background-color: #402c00;
  border: 2px solid #e2ae1e;
}

.fileTab :global(.adm-badge-content) {
  color: #e2ae1e;
}

.unbindContent {
  display: flex;
  align-items: center;
  flex-direction: column;
}

.unbindContent p {
  font-size: 16px;
  padding: 0 30px;
}

.unbindButton {
  display: flex;
  justify-content: center;
  width: 100%;
  padding: 0 30px;
}

.unbindButton :global(.adm-button) {
  margin: 0 10px;
  width: 40%;
  border-radius: 20px;
  background-color: #34bbc1;
  color: #fff;
  height: 50px;
}

.emptyContainer {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
}

.emptyImage {
  width: 100px;
  height: 64px;
  margin-bottom: 15px;
}

.emptyText {
  color: #999;
  font-size: 16px;
}

.addButton {
  position: fixed;
  bottom: 50px;
  right: 20px;
}

.addIcon {
  width: 50px;
  height: 50px;
}

// 排序面板样式
.filter_float_panel_container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: var(--background-color);
}

.filter_float_panel_navBar {
  display: flex;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid var(--border-color);
  position: relative;

  span {
    flex: 1;
    text-align: center;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
  }

  img {
    width: 20px;
    height: 20px;
    position: absolute;
    left: 16px;
    cursor: pointer;
  }
}

.filter_float_panel_content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.filter_float_panel_content_list_title {
  display: block;
  font-size: 14px;
  color: var(--secondary-text-color);
  margin-bottom: 8px;
}

.filter_float_panel_content_check_list_container {
  margin-bottom: 20px;

  :global {
    .adm-check-list {
      --border-inner: none;
      --border-bottom: none;
      --border-top: none;
    }

    .adm-list-item {
      padding: 5px 0;
    }
  }
}

// 自动上传引导页面样式
.autoUploadGuide {
  display: flex;
  flex-direction: column;
  height: 70%;
  padding: 0 24px;
}

.guideContent {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.guideIcons {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32px;
  gap: 16px;
}

.iconBlue {
  width: 64px;
  height: 64px;
  background-color: #4a90e2;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  &::after {
    content: "";
    width: 24px;
    height: 24px;
    background-color: #fff;
    border-radius: 4px;
  }
}

.iconOrange {
  width: 64px;
  height: 64px;
  background-color: #f5a623;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;

  &::after {
    content: "";
    width: 24px;
    height: 24px;
    background-color: #fff;
    border-radius: 50%;
  }
}

.iconArrow {
  font-size: 24px;
  color: #999;
  font-weight: bold;
}

.guideTitle {
  font-size: 18px;
  font-weight: 600;
  color: #000;
  margin-bottom: 24px;
  line-height: 1.4;
}

.guideDescription {
  font-size: 14px;
  color: #000;
  padding: 0 20px;
  margin-bottom: 40px;
}

.vipButton {
  height: 48px;
  border-radius: 16px;
  background-color: #402c00;
  border: none;
  font-size: 16px;
  font-weight: 600;
  color: #e2ae1e;

  &:active {
    background-color: #6b4220;
  }
}

.vipActiveButton {
  height: 48px;
  border-radius: 16px;
  background-color: #4a90e2;
  border: none;
  font-size: 16px;
  font-weight: 600;
  color: #fff;

  &:active {
    background-color: #357abd;
  }
}

// 文件列表容器
.fileListWrapper {
  display: flex;
  flex-direction: column;
  height: calc(100vh - 280px); // 调整高度，减去上方元素的总高度
  scrollbar-width: none;
  overflow-y: auto;
  padding-bottom: 50px;
}

// 面包屑和全选按钮所在的行
.breadcrumbRow {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 24px;
  position: sticky;
  top: 0;
  z-index: 10;
  background-color: var(--background-color);
}

// 面包屑容器
.breadcrumbContainer {
  display: flex;
  flex-direction: row;
  align-items: center;
  overflow-x: auto;
  flex: 1;
  white-space: nowrap;

  &::-webkit-scrollbar {
    display: none;
  }
}

// 全选按钮容器
.selectAllButton {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-left: 15px;
}

.breadcrumbContent {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.breadcrumbNextContainer {
  display: flex;
  align-items: center;
  margin: 0 3px;
}

.breadcrumbNextImg {
  // width: 16px;
  height: 16px;
}

.breadcrumbItem {
  font-size: 14px;
  color: rgba(140, 147, 176, 1);
  cursor: pointer;
  padding: 5px 10px;
  border-radius: 20px;
  background-color: rgba(140, 147, 176, 0.1);

  // &:hover {
  //   background-color: rgba(0, 0, 0, 0.05);
  // }
}

.breadcrumbItemActive {
  color: rgba(255, 178, 29, 1);
  font-weight: 500;
  background-color: rgba(255, 178, 29, 0.15);
}
