import MonitorPlayer, { splitURL } from "@/components/CameraPlayer/components/MonitorPlayer/MonitorPlayer";
import styles from "./index.module.scss";
import Player from "xgplayer/es/player";
import { useCallback, useEffect, useMemo, useRef, useState } from "react";
import EventOperate from "@/components/CameraPlayer/components/EventOperate/EventOperate";
import TimeAxis, { IEventBlock } from "@/components/CameraPlayer/components/TimeAxis/TimeAxis";
import EventListCard, { IEventListCard } from "@/components/CameraPlayer/components/EventList/EventListCard";
import { PreloadImage } from "@/components/Image";
import all from "@/Resources/player/all_.png";
import PopoverSelector from "@/components/PopoverSelector";
import { eventDefinition, eventSelectorOptions } from "@/components/CameraPlayer/constants";
import { ICameraDetail, IEventVideo, ILiveCameraData } from "../../IPC_APP/CameraDetail";
import { zhCN } from "date-fns/locale";
import { format } from "date-fns";
import { ICollapsePanel } from "@/layouts/Layout";
import { ApiResponse, startLiveWithCamera } from "@/api/ipc";
import { useBoolean, useInViewport, useRequest, useUpdateEffect } from 'ahooks';
import { modalShow } from "@/components/List";
import { Toast } from "@/components/Toast/manager";
import { callTaskCenter, downloadAndSaveWithPlayer } from "@/api/cameraPlayer";
import { getDeviceType } from "@/utils/DeviceType";
import { move2trashbin } from "@/api/fatWall";



interface IDesktopCameraDetail {
  cameraItem?: (ICollapsePanel & ICameraDetail);
}

interface ICurCamera {
  url: string;
  camera_lens: string[]
}

const deleteFontColor = 'rgba(243, 0, 24, 1)';

const IPCDesktopCameraDetail = (props: IDesktopCameraDetail) => {
  const { cameraItem } = props;
  const cameraRef1 = useRef<Player | null>(null);
  const cameraRef2 = useRef<Player | null>(null);
  const cameraRef3 = useRef<Player | null>(null);
  const [eventData, setEventData] = useState<IEventVideo[]>([]);
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const containerRef = useRef<HTMLDivElement>(null);
  const [currentWidth, setCurrentWidth] = useState<number>(0);
  const [eventSelectorVisible, setEventSelectorVisible] = useState<boolean>(false);
  const [eventSelectorValue, setEventSelectorValue] = useState<string>('');
  const [activeVideo, setActiveVideo] = useState<any>(undefined); // 当前播放的视频
  const [cameraConfig, setCameraConfig] = useState<any>({});
  const [isPause, setIsPause] = useState<boolean>(false);
  const [liveCameraData, setLiveCameraData] = useState<ILiveCameraData>(); // 开启直播获得数据
  const [curDevice, setCurDevice] = useState<(ICollapsePanel & ICameraDetail & ICurCamera) | null>(null);
  const [curTime, setCurTime] = useState<Date>(new Date()); // 获得时间轴当前时间

  // 控制是否自动加载
  const loaderRef = useRef<HTMLDivElement>(null);
  const [hasMore, setHasMore] = useState(false);

  // 获取当前时间轴事件id
  const [currentEventKey, setCurrentEventKey] = useState<string>('');

  // pc端视频弹窗播放
  const pc_container_ref = useRef(null);
  const [state, { setTrue, setFalse }] = useBoolean(false);
  const pc_movie_container_ref = useRef<Player | null>(null);
  const pc_movie_ref = useRef<string>('');

  useEffect(() => {
    if (!containerRef.current) return;
    const resizeObserver = new ResizeObserver((entries) => {
      const entry = entries[0];
      if (entry.contentRect.width > 0) {
        setCurrentWidth(entry.contentRect.width / 2);
      }
    });

    resizeObserver.observe(containerRef.current);
    return () => resizeObserver.disconnect();
  }, [])

  //右侧事件筛选回调
  const eventSelectorOnChange = useCallback((value: string) => {
    setEventSelectorValue(value);
    setEventSelectorVisible(false);
  }, [])

  const openVideo = useCallback((item) => {
    pc_movie_ref.current = item.file;
    setTrue();
  }, [setTrue])

  const delVideo = useCallback((item) => {
    modalShow('是否确认删除视频?', '', async (m) => {
      const res = await move2trashbin({ path: [item.file] }).catch((e) => { Toast.show('删除失败'); });
      if (res && res.code === 0) {
        Toast.show('文件已移至回收站中！');
        m.destroy();
        return;
      }
    }, () => null, false, { okBtnStyle: { background: 'var(--cancel-btn-background-color)', color: deleteFontColor } })
  }, [])

  const shareVideo = useCallback(async (item) => {
    try {
      await navigator.clipboard.writeText(item.file);
      Toast.show('复制成功');
    } catch (e) {
      console.log('shareErr', e);
    }
  }, [])

  const downloadVideo = useCallback(async (item) => {
    await downloadAndSaveWithPlayer([item.file], async (r) => {
      if (r.code === 0) {
        console.log('正在下载，调起任务中心');
        await callTaskCenter();
      }
    })
  }, [])

  // 时间轴事件
  const timeAxisEvents = useMemo(() => {
    const tempObj: { [key: string]: IEventBlock[] } = {}
    curDevice?.camera_lens.forEach((item) => {
      tempObj[item] = eventData.filter((it) => it.camera_lens === item).map((it) => {
        const date = new Date(Number(it.time));
        const h = date.getHours();
        const m = date.getMinutes();
        const s = date.getSeconds();
        return {
          ...it,
          camera_lens: it.camera_lens,
          event_name: it.event_name,
          day: date.getDate(),
          start: h * 3600 + m * 60 + s, end: h * 3600 + m * 60 + s + it.media_duration,
          color: it.event_name !== '' ? eventDefinition[it.event_name].color : 'rgba(229, 229, 229, 1)',
          file: it.file,
          time: it.time
        }
      })
    })
    console.log('事件属性解析成功', tempObj);
    return tempObj;
  }, [curDevice?.camera_lens, eventData])

  const recordOpt = useMemo(() => {
    if (!activeVideo) return undefined;
    let event: IEventVideo | undefined;
    if (activeVideo.id) {
      event = eventData.find((it) => it.id === activeVideo.id);
    }
    return {
      isRecord: activeVideo,
      recordTime: event ? Number(event.time) : 0,
      event: event
    }
  }, [activeVideo, eventData])

  useEffect(() => {
    if (curDevice) {
      setCameraConfig({
        width: "870px", url: curDevice.url, type: 'Live', mediaName: curDevice.key + curDevice.name
      })
    }
  }, [curDevice, curDevice?.url, curDevice?.key, curDevice?.name])

  const cameras = useMemo(() => {
    const temp = cameraItem?.key_frame.map((it) => {
      return {
        id: it.lens_id,
        camera_lens: `${cameraItem.did}_${it.lens_id}`,
        frame: it.frame
      }
    })
    return temp;
  }, [cameraItem?.did, cameraItem?.key_frame])

  const dualOptions = useMemo(() => {
    if (curDevice && curDevice.key_frame.length > 1) {
      return {
        urls: {
          main: curDevice && liveCameraData ? liveCameraData[`${curDevice?.key}_0`].hls_file : '',
          secondary: curDevice && liveCameraData ? liveCameraData[`${curDevice?.key}_1`]?.hls_file : '',
          third: curDevice && liveCameraData && curDevice.key_frame[2] ? liveCameraData[`${curDevice?.key}_2`].hls_file : '',
        },
        poster: {
          main: curDevice && liveCameraData ?
            liveCameraData[`${curDevice?.key}_1`].key_frame ? liveCameraData[`${curDevice?.key}_1`]?.key_frame[0]?.frame : '' : '',
          secondary: curDevice && liveCameraData ?
            liveCameraData[`${curDevice?.key}_0`].key_frame ? liveCameraData[`${curDevice?.key}_0`]?.key_frame[0]?.frame : '' : '',
          third: curDevice && liveCameraData && curDevice.key_frame[2] ?
            liveCameraData[`${curDevice?.key}_2`].key_frame ? liveCameraData[`${curDevice?.key}_2`]?.key_frame[0]?.frame : '' : '',
        },
        psm: {
          main: curDevice && curDevice.key_frame[0] ? curDevice.key_frame[0].psm : false,
          secondary: curDevice && curDevice.key_frame[1] ? curDevice.key_frame[1].psm : false,
          third: curDevice && curDevice.key_frame[2] ? curDevice.key_frame[2].psm : false,
        },
        cameraRef_secondary: cameraRef2,
        cameraRef_third: cameraRef3
      }
    }
    return undefined;
  }, [curDevice, liveCameraData])

  const { runAsync } = useRequest(startLiveWithCamera, {
    manual: true,
    debounceWait: 300, // 防抖时间
  })

  const searchCurDeviceInfo = useCallback(async (time?: string) => {
    if (!cameraItem || !cameras) return;

    // 如果设备在线时才调直播接口
    if (!cameraItem.isOnline) {
      setCurDevice({
        ...cameraItem,
        url: '',
        camera_lens: cameraItem.key_frame.map((it) => `${cameraItem.did}_${it.lens_id}`)
      })

      return;
    }
    const startLiveRes: ApiResponse<ILiveCameraData> | null = await runAsync(cameras.map((it) => it.camera_lens), time).then().catch(() => null);
    console.log('开始直播!', `当前请求直播时间为:${time ? time : '此刻'}`)
    if (startLiveRes && startLiveRes.code === 0 && startLiveRes.data) {
      console.log('查询成功,摄像头开启直播信息:', startLiveRes.data);
      setLiveCameraData(startLiveRes.data);
      setCurDevice({
        ...cameraItem,
        url: startLiveRes.data[`${cameraItem.did}_0`].hls_file,
        camera_lens: cameraItem.key_frame.map((it) => `${cameraItem.did}_${it.lens_id}`)
      })
    }
  }, [cameraItem, cameras, runAsync])

  useEffect(() => {
    searchCurDeviceInfo();
  }, [searchCurDeviceInfo])

  const backLive = useCallback(() => {
    console.log('回到Live模式');
    setActiveVideo(undefined);
    searchCurDeviceInfo();
  }, [searchCurDeviceInfo])

  const eventList: IEventListCard[] = useMemo(() => {
    const temp: (IEventListCard)[] = [];
    eventData.filter((it) => it.event_name !== '').forEach(async (it) => {
      temp.push({
        id: it.id,
        icon: eventDefinition[it.event_name].icon,
        title: eventDefinition[it.event_name].label,
        subtitle: format(new Date(Number(it.time)), 'HH:mm', { locale: zhCN }),
        poster: `${it.cover_file}/original`,
        name: it.event_name,
        file: it.file,
        urls: eventData.filter((item) => item.event_name === it.event_name).map((it) => it.file)
      })
    })
    console.log('今日事件列表:', temp);
    return temp;
  }, [eventData])

  // 滚动到底部加载更多数据
  const [inViewport] = useInViewport(loaderRef, {
    threshold: 0.2,
  })

  useUpdateEffect(() => {
    if (inViewport) {
    }
  }, [inViewport]);

  return (
    <div className={styles.container}>
      <div className={styles.left} ref={containerRef}>
        {
          curDevice && (
            <MonitorPlayer selectedDate={selectedDate} setSelectedDate={setSelectedDate} poster={curDevice.key_frame.find((it) => it.lens_id === 0)?.frame}
              setIsPause={setIsPause} initPlayStatus={false}
              baseConfig={cameraConfig} cameraRef={cameraRef1} dashboardDual={true} dualOptions={dualOptions} cameraDetail={curDevice}
              isOnline={curDevice.isOnline} okCallback={() => searchCurDeviceInfo(String(curTime.getTime()))} />
          )
        }
        <div className={styles.eventOperate}>
          {
            curDevice && (
              <EventOperate currentEventKey={currentEventKey} controlEventFilter={setEventData} setSelectedDate={setSelectedDate} selectedDate={selectedDate}
                style={{ background: 'var(--card-background-color)' }} camera_lens={curDevice?.camera_lens} eventData={eventData} parentLoaderRef={loaderRef} parentLoaderCallback={setHasMore}>
                <div className={styles.timeAxisContainer}>
                  <TimeAxis setCurTime={setCurTime} isPause={isPause} setConfig={setCameraConfig} eventData={eventData} type={'Live'} onChange={setCurrentEventKey}
                    isFull={false} deviceType={getDeviceType() ? 'pc' : 'mobile'} current={selectedDate} setCurrent={setSelectedDate} setRecordEvent={setActiveVideo}
                    currentWidth={currentWidth} playerOpt={
                      {
                        player: { main: cameraRef1, secondary: cameraRef2 },
                        url: liveCameraData ? {
                          main: liveCameraData[`${curDevice.did}_0`].hls_file,
                          secondary: liveCameraData[`${curDevice.did}_1`]?.hls_file
                        } : { main: '', secondary: '' }
                      }
                    } events={timeAxisEvents} recordOpt={recordOpt} />
                </div>
              </EventOperate>
            )
          }
        </div>
      </div>
      <div className={styles.right}>
        <div className={styles.right_header}>
          <div className={styles.right_title}>今日片段</div>
          <div className={styles.right_opt}>
            <div className={styles.backLive} onClick={backLive}>
              实时
            </div>
            <PopoverSelector visible={eventSelectorVisible} onVisibleChange={setEventSelectorVisible} value={eventSelectorValue}
              options={eventSelectorOptions} onChange={eventSelectorOnChange} theme={
                {
                  active: 'rgba(52, 130, 255, 0.5)',
                  selectedColor: 'rgba(52, 130, 255, 1)'
                }}>
              <div className={styles.right_opt_filter}>
                <PreloadImage src={all} alt="right_opt" />
              </div>
            </PopoverSelector>
          </div>
        </div>
        <div>
          {
            eventList.map((item, index) => {
              return <EventListCard {...item} poster={item.poster && splitURL(item.poster)} posterCallback={item => setActiveVideo(item)} posterActive={activeVideo?.id === item.id}
                key={`${item.name}_${item.id}_${item.file}_${index}` || 'eventList' + index} moreOpt={{
                  moreOptOptions: [
                    { label: '打开视频', value: 'open', callback: openVideo },
                    { label: '下载视频', value: 'download', callback: downloadVideo },
                    { label: '分享视频', value: 'share', callback: shareVideo },
                    { label: '删除视频', value: 'delete', callback: delVideo, color: deleteFontColor },
                  ],
                  curValue: ''
                }} needHeader={true} />
            })
          }
          {hasMore && (<div ref={loaderRef} style={{ padding: '20px', height: '20px' }}></div>)}
        </div>
      </div>

      {/*视频全屏播放div */}
      <div style={{ display: state ? 'flex' : 'none' }} ref={pc_container_ref} className={styles.temp_video_container}>
        <MonitorPlayer baseConfig={{
          width: '1210px',
          url: pc_movie_ref.current,
          type: "Movie",
          mediaName: "temp_video"
        }} cameraRef={pc_movie_container_ref} options={{
          type: 'modal', onClose: () => {
            pc_movie_ref.current = '';
            setFalse();
          }
        }} />
      </div>
    </div>
  )
}

export default IPCDesktopCameraDetail;