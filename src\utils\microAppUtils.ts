/**
 * 判断是否在微前端环境中
 */
export const isMicroAppEnvironment = (): boolean => {
  return !!window.__MICRO_APP_ENVIRONMENT__;
};

/**
 * 根据当前环境发送数据
 * @param appMethod - App 环境的方法名和参数
 * @param pcMethod - 微前端环境的方法名和参数
 * @param callback - 回调函数
 */
export const sendToEnvironment = (
  appMethod: { methodName: string; params: Record<string, any> },
  pcMethod: { params: { cmd: string;[key: string]: any } },
  callback?: (response: any) => void
): void => {
  if (isMicroAppEnvironment()) {
    sendDataToPc(pcMethod.params, callback);
  } else {
    sendDataToApp(appMethod.methodName, appMethod.params, callback);
  }
};
/**
 * App 环境：发送数据
 * @param methodName - App 环境的方法名
 * @param params - 参数
 * @param callback - 回调函数
 */
export const sendDataToApp = (
  methodName: string,
  params: Record<string, any>,
  callback?: (response: any) => void
): void => {
  console.log(`App 环境中发送数据，方法: ${methodName}, 参数:`, params);
  window.hs_callHandler?.(methodName, params, (response: string) => {
    try {
      const resultData = JSON.parse(response);
      console.log(`App 返回的数据:`, resultData);
      if (callback) {
        callback(resultData);
      }
    } catch (error) {
      console.error('解析 App 返回结果失败:', error);
      if (callback) {
        callback({ code: -1, message: '解析失败' });
      }
    }
  });
};
/**
 * 微前端PC环境：发送数据
 * @param params - 参数对象，例如 { cmd: "download", spath: "/admin/shipin.mov" }
 * @param callback - 回调函数
 */
export const sendDataToPc = (
  params: { cmd: string;[key: string]: any }, // 参数对象，必须包含 cmd 字段
  callback?: (response: any) => void
): void => {
  console.log(`微前端环境中发送数据，参数:`, params);
  window.microApp?.dispatch({ params });

  if (callback) {
    // 如果需要监听主应用的返回
    window.microApp?.addDataListener((data) => {
      console.log('接收到主应用的返回数据:', data);
      callback(data);
    });
  }
};
/**
 * 移除微前端环境的监听器
 */
export const removeMicroPcListener = (): void => {
  if (isMicroAppEnvironment()) {
    console.log('移除微前端环境的监听器');
    window.microApp?.removeDataListener();
  }
};

/**
 * TV端专用：App 环境发送字符串数据
 * @param methodName - App 环境的方法名
 * @param paramsString - 字符串格式的参数
 * @param callback - 回调函数
 */
export const sendStringDataToApp = (
  methodName: string,
  paramsString: string,
  callback?: (response: any) => void
): void => {
  console.log(`TV端App 环境中发送字符串数据，方法: ${methodName}, 参数:`, paramsString);
  window.hs_callHandler?.(methodName, paramsString, (response: string) => {
    try {
      const resultData = JSON.parse(response);
      console.log(`App 返回的数据:`, resultData);
      if (callback) {
        callback(resultData);
      }
    } catch (error) {
      console.error('解析 App 返回结果失败:', error);
      if (callback) {
        callback({ code: -1, message: '解析失败' });
      }
    }
  });
};

/**
 * TV端专用：微前端PC环境发送字符串数据
 * @param paramsString - 字符串格式的参数
 * @param callback - 回调函数
 */
export const sendStringDataToPc = (
  paramsString: string,
  callback?: (response: any) => void
): void => {
  console.log(`TV端微前端环境中发送字符串数据，参数:`, paramsString);
  window.microApp?.dispatch({ params: paramsString });

  if (callback) {
    // 如果需要监听主应用的返回
    window.microApp?.addDataListener((data) => {
      console.log('接收到主应用的返回数据:', data);
      callback(data);
    });
  }
};

/**
 * TV端专用：根据当前环境发送字符串数据
 * @param appMethod - App 环境的方法名和字符串参数
 * @param pcParamsString - 微前端环境的字符串参数
 * @param callback - 回调函数
 */
export const sendStringToEnvironment = (
  appMethod: { methodName: string; paramsString: string },
  pcParamsString: string,
  callback?: (response: any) => void
): void => {
  if (isMicroAppEnvironment()) {
    sendStringDataToPc(pcParamsString, callback);
  } else {
    sendStringDataToApp(appMethod.methodName, appMethod.paramsString, callback);
  }
};


/**
 * App 环境：注册供 App 调用的方法
 * @param methodName - App 环境的方法名
 * @param handler - 回调函数
 */
export const registerAppMethod = (methodName: string, handler: (params: any) => void): void => {
  console.log(`App 环境中注册供调用的方法: ${methodName}`);
  window.hs_registerHandler?.(methodName, (params: any) => {
    console.log(`App 调用了注册的方法: ${methodName}, 参数:`, params);
    handler(params);
  });
};