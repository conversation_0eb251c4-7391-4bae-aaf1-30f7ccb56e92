.container {
  height: 100%;
  background-color: #ffffff;
  padding-top: 35px;
}

.content {
  padding: 20px;
}

.step {
  margin-bottom: 40px;
}

.stepTitle {
  font-size: 20px;
  font-weight: 600;
  color: #000;
  margin-bottom: 8px;
}

.stepDescription {
  font-size: 14px;
  color: #666;
  line-height: 1.5;
  margin-bottom: 20px;
}

/* 第一步 - 设备连接 */
.deviceConnection {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e8e8e8;
  border-radius: 12px;
  padding: 30px 20px;
  margin-bottom: 20px;
}

.deviceGroup {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.deviceIcon {
  width: 80px;
  height: 60px;
  background-color: #333;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 12px;
  position: relative;
}

.computerIcon {
  font-size: 32px;
  color: #fff;
}

.routerIcon {
  font-size: 28px;
  color: #fff;
}

.deviceLabel {
  font-size: 12px;
  color: #333;
  text-align: center;
  margin: 0;
}

.connectionLine {
  width: 60px;
  height: 2px;
  background-color: #999;
  margin: 0 20px;
}

/* 第二步 - IP地址显示 */
.ipDisplay {
  background-color: #e8e8e8;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin-bottom: 20px;
}

/* 第三步 - 验证码显示 */
.codeDisplay {
  background-color: #e8e8e8;
  border-radius: 12px;
  padding: 30px 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 16px;
  margin-bottom: 40px;
}

.digit {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  min-width: 24px;
  text-align: center;
}

/* 按钮样式 */
.buttonSection {
  padding: 0 20px;
  margin-bottom: 40px;
}

.refreshButton {
  width: 100%;
  height: 48px;
  border-radius: 24px;
  font-size: 16px;
  font-weight: 500;

  :global {
    .adm-button {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      border: none;
      color: #fff;

      &:active {
        background: linear-gradient(135deg, #096dd9 0%, #1890ff 100%);
      }
    }
  }
}
